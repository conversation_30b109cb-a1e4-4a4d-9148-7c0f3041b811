# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/buddychip
DIRECT_URL=postgresql://user:password@localhost:5432/buddychip

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_...
CLERK_WEBHOOK_SIGNING_SECRET=whsec_...

# AI & OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key
OR_SITE_URL=your_site_url
OR_APP_NAME=BuddyChip

# AI Tools
OPENAI_API_KEY=your_openai_api_key
XAI_API_KEY=your_xai_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
EXA_API_KEY=your_exa_api_key

# File Storage
UPLOADTHING_TOKEN=your_uploadthing_token

# Twitter API (TwitterAPI.io)
TWITTER_API_KEY=your_twitterapi_io_api_key

# Memory Store (Mem0)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE=your_supabase_service_role_key
MEM0_API_KEY=your_mem0_api_key

# Rate Limiting
KV_URL=your_upstash_kv_url
KV_TOKEN=your_upstash_kv_token

# Sentry Error Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# Logging Configuration
VERBOSE_LOGGING=false
ENABLE_PRISMA_QUERY_LOGS=false
ENABLE_CONTEXT_LOGS=false
ENABLE_TRPC_REQUEST_LOGS=false
