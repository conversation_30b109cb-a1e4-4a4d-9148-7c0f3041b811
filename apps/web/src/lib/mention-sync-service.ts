/**
 * Mention Sync Service for BuddyChip
 * 
 * Handles fetching and synchronizing mentions from Twitter API for monitored accounts
 * Includes deduplication, error handling, and proper data transformation
 */

import type { PrismaClient } from '../../prisma/generated/index.js';
import { twitterClient } from './twitter-client';
import type { TwitterTweet } from './twitter-client';

export interface SyncResult {
  success: boolean;
  accountHandle: string;
  newMentions: number;
  totalMentions: number;
  limitReached?: boolean;
  maxAllowed?: number;
  currentCount?: number;
  error?: string;
}

export interface BulkSyncResult {
  success: boolean;
  results: SyncResult[];
  totalNewMentions: number;
  errors: string[];
}

export class MentionSyncService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Fetch and store a user's recent tweets
   * Useful for analyzing a user's tweet patterns or content
   */
  async fetchUserTweets(
    userId: string,
    username: string,
    options: {
      limit?: number;
      includeReplies?: boolean;
      includeRetweets?: boolean;
      cursor?: string;
    } = {}
  ) {
    try {
      console.log(`🔄 Fetching tweets for user: @${username}`);

      // Fetch tweets from Twitter API
      const response = await twitterClient.getUserLastTweets(username, {
        limit: options.limit || 20,
        includeReplies: options.includeReplies ?? false,
        includeRetweets: options.includeRetweets ?? true,
        cursor: options.cursor,
      });

      if (!response.tweets || response.tweets.length === 0) {
        return {
          success: true,
          tweets: [],
          newTweets: 0,
          nextCursor: null,
          hasNextPage: false,
        };
      }

      // Store tweets as mentions with isUserTweet flag
      const storedTweets = [];
      let newTweetCount = 0;

      for (const tweet of response.tweets) {
        try {
          const existingMention = await this.prisma.mention.findUnique({
            where: { id: tweet.id },
          });

          if (!existingMention) {
            const mention = await this.prisma.mention.create({
              data: {
                id: tweet.id,
                userId,
                link: `https://x.com/${tweet.author.userName}/status/${tweet.id}`,
                content: tweet.text,
                authorHandle: tweet.author.userName,
                authorName: tweet.author.name,
                authorId: tweet.author.id,
                authorAvatarUrl: tweet.author.profilePicture,
                authorVerified: tweet.author.isBlueVerified,
                mentionedAt: new Date(tweet.createdAt),
                replyCount: tweet.replyCount || 0,
                retweetCount: tweet.retweetCount || 0,
                likeCount: tweet.likeCount || 0,
                isReply: tweet.isReply || false,
                parentTweetId: tweet.inReplyToId,
                processed: false,
                isUserTweet: true, // Mark as user's own tweet
              },
            });
            storedTweets.push(mention);
            newTweetCount++;
          } else {
            // Update existing mention with latest metrics
            const updated = await this.prisma.mention.update({
              where: { id: tweet.id },
              data: {
                replyCount: tweet.replyCount || 0,
                retweetCount: tweet.retweetCount || 0,
                likeCount: tweet.likeCount || 0,
                updatedAt: new Date(),
              },
            });
            storedTweets.push(updated);
          }
        } catch (error) {
          console.error(`Failed to store tweet ${tweet.id}:`, error);
        }
      }

      console.log(`✅ Fetched ${response.tweets.length} tweets, stored ${newTweetCount} new tweets`);

      return {
        success: true,
        tweets: storedTweets,
        newTweets: newTweetCount,
        totalTweets: response.tweets.length,
        nextCursor: response.next_cursor,
        hasNextPage: response.has_next_page,
      };
    } catch (error) {
      console.error(`❌ Error fetching tweets for @${username}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        tweets: [],
        newTweets: 0,
      };
    }
  }

  /**
   * Get user's subscription limits for mention syncing
   */
  private async getUserSyncLimits(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        plan: {
          include: {
            features: {
              where: {
                feature: {
                  in: ['MENTIONS_PER_SYNC', 'MAX_TOTAL_MENTIONS']
                }
              }
            }
          }
        }
      }
    });

    if (!user?.plan) {
      // Default limits if no plan found
      return {
        mentionsPerSync: 25,
        maxTotalMentions: 100
      };
    }

    const mentionsPerSync = user.plan.features.find(f => f.feature === 'MENTIONS_PER_SYNC')?.limit || 25;
    const maxTotalMentions = user.plan.features.find(f => f.feature === 'MAX_TOTAL_MENTIONS')?.limit || 100;

    return {
      mentionsPerSync,
      maxTotalMentions,
      planName: user.plan.name
    };
  }

  /**
   * Get current unreplied mention count for an account
   */
  private async getAccountUnrepliedCount(accountId: string): Promise<number> {
    return await this.prisma.mention.count({
      where: {
        accountId: accountId,
        archived: false,  // Only count non-archived mentions
        responses: {
          none: {
            used: true  // Count mentions without used responses
          }
        }
      }
    });
  }

  /**
   * Sync mentions for a specific monitored account
   */
  async syncAccountMentions(accountId: string, userId: string): Promise<SyncResult> {
    try {
      console.log(`🔄 Starting mention sync for account ${accountId}`);

      // Get user's subscription limits
      const limits = await this.getUserSyncLimits(userId);
      console.log(`📊 User limits: ${limits.mentionsPerSync} per sync, ${limits.maxTotalMentions} max total (Plan: ${limits.planName || 'Default'})`);

      // Get the monitored account
      const account = await this.prisma.monitoredAccount.findFirst({
        where: {
          id: accountId,
          userId: userId,
          isActive: true,
        },
      });

      if (!account) {
        throw new Error('Monitored account not found or inactive');
      }

      console.log(`📋 Syncing mentions for @${account.twitterHandle}`);

      // Check current unreplied mention count for this account
      const currentUnrepliedCount = await this.getAccountUnrepliedCount(accountId);
      console.log(`📈 Current unreplied mentions for @${account.twitterHandle}: ${currentUnrepliedCount}/${limits.maxTotalMentions}`);

      // Check if we've reached the max total limit
      if (currentUnrepliedCount >= limits.maxTotalMentions) {
        console.log(`⚠️ Max total mentions limit reached for @${account.twitterHandle}`);
        return {
          success: true,
          accountHandle: account.twitterHandle,
          newMentions: 0,
          totalMentions: currentUnrepliedCount,
          limitReached: true,
          maxAllowed: limits.maxTotalMentions,
          currentCount: currentUnrepliedCount,
          error: `Maximum total mentions limit (${limits.maxTotalMentions}) reached for this account`
        };
      }

      // Calculate how many mentions we can actually fetch
      const remainingCapacity = limits.maxTotalMentions - currentUnrepliedCount;
      const fetchLimit = Math.min(limits.mentionsPerSync, remainingCapacity);
      
      console.log(`🎯 Fetching up to ${fetchLimit} mentions (sync limit: ${limits.mentionsPerSync}, remaining capacity: ${remainingCapacity})`);

      // Fetch mentions from Twitter API
      const mentionsResponse = await twitterClient.getUserMentions(account.twitterHandle, {
        limit: fetchLimit,
      });

      console.log(`📥 Fetched ${mentionsResponse.tweets.length} mentions from Twitter API`);

      let newMentionsCount = 0;
      const mentionsToCreate = [];

      // Check if user has AI analysis enabled based on plan
      const userLimits = await this.getUserSyncLimits(userId);
      const enableAIAnalysis = userLimits.planName !== 'Reply Guy'; // Enable for Pro and higher plans
      
      console.log(`🤖 AI Analysis: ${enableAIAnalysis ? 'Enabled' : 'Disabled'} for plan: ${userLimits.planName}`);

      // Process each mention
      for (const tweetData of mentionsResponse.tweets) {
        try {
          // Check if mention already exists
          const existingMention = await this.prisma.mention.findUnique({
            where: { id: tweetData.id },
          });

          if (existingMention) {
            console.log(`⏭️ Mention ${tweetData.id} already exists, skipping`);
            continue;
          }

          // Prepare mention data with optional AI analysis
          let mentionData;
          if (enableAIAnalysis) {
            mentionData = await this.transformTweetToMentionWithAnalysis(tweetData, account.id, userId, true);
          } else {
            mentionData = this.transformTweetToMention(tweetData, account.id, userId);
          }
          
          mentionsToCreate.push(mentionData);
          newMentionsCount++;

          const analysisInfo = 'bullishScore' in mentionData && mentionData.bullishScore ? 
            ` (Bullish: ${mentionData.bullishScore}, Importance: ${'importanceScore' in mentionData ? mentionData.importanceScore : 'N/A'})` : '';
          console.log(`✅ Prepared new mention: ${tweetData.id}${analysisInfo}`);
        } catch (error) {
          console.error(`❌ Error processing mention ${tweetData.id}:`, error);
          // Continue processing other mentions
        }
      }

      // Bulk create new mentions (can't use createMany with JSON fields, so create individually)
      if (mentionsToCreate.length > 0) {
        let createdCount = 0;
        for (const mentionData of mentionsToCreate) {
          try {
            await this.prisma.mention.create({
              data: mentionData,
            });
            createdCount++;
          } catch (error) {
            console.error(`❌ Failed to create mention ${mentionData.id}:`, error);
            // Continue with other mentions
          }
        }
        console.log(`💾 Created ${createdCount}/${mentionsToCreate.length} new mentions in database`);
      }

      // Update account's last checked timestamp and mention count
      await this.prisma.monitoredAccount.update({
        where: { id: accountId },
        data: {
          lastCheckedAt: new Date(),
          totalMentions: {
            increment: newMentionsCount,
          },
        },
      });

      // Get total mentions count for this account
      const totalMentions = await this.prisma.mention.count({
        where: {
          accountId: accountId,
          userId: userId,
        },
      });

      console.log(`🎉 Sync completed for @${account.twitterHandle}: ${newMentionsCount} new, ${totalMentions} total`);

      // Get final unreplied count after sync
      const finalUnrepliedCount = await this.getAccountUnrepliedCount(accountId);
      const limitReached = finalUnrepliedCount >= limits.maxTotalMentions;

      return {
        success: true,
        accountHandle: account.twitterHandle,
        newMentions: newMentionsCount,
        totalMentions: totalMentions,
        limitReached: limitReached,
        maxAllowed: limits.maxTotalMentions,
        currentCount: finalUnrepliedCount,
      };
    } catch (error) {
      console.error(`❌ Sync failed for account ${accountId}:`, error);
      return {
        success: false,
        accountHandle: 'Unknown',
        newMentions: 0,
        totalMentions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Sync mentions for all monitored accounts of a user
   */
  async syncAllUserAccounts(userId: string): Promise<BulkSyncResult> {
    try {
      console.log(`🚀 Starting bulk sync for user ${userId}`);

      // Get all active monitored accounts for the user
      const accounts = await this.prisma.monitoredAccount.findMany({
        where: {
          userId: userId,
          isActive: true,
        },
        orderBy: {
          lastCheckedAt: 'asc', // Sync accounts that haven't been checked recently first
        },
      });

      console.log(`📋 Found ${accounts.length} active accounts to sync`);

      if (accounts.length === 0) {
        return {
          success: true,
          results: [],
          totalNewMentions: 0,
          errors: [],
        };
      }

      const results: SyncResult[] = [];
      const errors: string[] = [];
      let totalNewMentions = 0;

      // Sync each account sequentially to avoid rate limiting
      for (const account of accounts) {
        try {
          console.log(`⏳ Syncing account ${account.id} (@${account.twitterHandle})...`);
          
          const result = await this.syncAccountMentions(account.id, userId);
          results.push(result);
          
          if (result.success) {
            totalNewMentions += result.newMentions;
          } else if (result.error) {
            errors.push(`@${account.twitterHandle}: ${result.error}`);
          }

          // Add a small delay between requests to respect rate limits
          await this.sleep(1000); // 1 second delay
        } catch (error) {
          const errorMessage = `@${account.twitterHandle}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          console.error(`❌ Failed to sync account @${account.twitterHandle}:`, error);
        }
      }

      console.log(`🏁 Bulk sync completed: ${totalNewMentions} total new mentions, ${errors.length} errors`);

      return {
        success: errors.length === 0,
        results: results,
        totalNewMentions: totalNewMentions,
        errors: errors,
      };
    } catch (error) {
      console.error(`❌ Bulk sync failed for user ${userId}:`, error);
      return {
        success: false,
        results: [],
        totalNewMentions: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  /**
   * Transform Twitter API tweet data to database mention format
   */
  private transformTweetToMention(tweet: TwitterTweet, accountId: string, userId: string) {
    return {
      id: tweet.id,
      userId: userId,
      accountId: accountId,
      link: tweet.url || `https://x.com/${tweet.author.userName}/status/${tweet.id}`,
      content: tweet.text,
      authorHandle: tweet.author.userName,
      authorName: tweet.author.name,
      authorId: tweet.author.id,
      authorAvatarUrl: tweet.author.profilePicture,
      authorVerified: tweet.author.isBlueVerified,
      mentionedAt: new Date(tweet.createdAt),
      replyCount: tweet.replyCount || 0,
      retweetCount: tweet.retweetCount || 0,
      likeCount: tweet.likeCount || 0,
      isReply: Boolean(tweet.inReplyToUserId),
      parentTweetId: tweet.inReplyToUserId,
      processed: false,
    };
  }

  /**
   * Transform Twitter API tweet data with AI analysis
   */
  private async transformTweetToMentionWithAnalysis(
    tweet: TwitterTweet, 
    accountId: string, 
    userId: string,
    enableAIAnalysis: boolean = true
  ) {
    const baseMention = this.transformTweetToMention(tweet, accountId, userId);
    
    if (!enableAIAnalysis) {
      return baseMention;
    }

    try {
      // Get AI analysis for the tweet
      const { getBenjiForUser } = await import('./benji-agent');
      const benji = await getBenjiForUser(userId);
      
      const analysis = await benji.performFullAnalysis(tweet.text, {
        name: tweet.author.name,
        handle: tweet.author.userName,
        followers: tweet.author.followers,
        verified: tweet.author.isBlueVerified,
        avatarUrl: tweet.author.profilePicture,
      });

      console.log(`🤖 AI Analysis completed for tweet ${tweet.id}:`, {
        bullishScore: analysis.bullishScore,
        importanceScore: analysis.importanceScore,
        priority: analysis.analysisData.priority,
        keywordCount: analysis.keywords.length
      });

      return {
        ...baseMention,
        bullishScore: analysis.bullishScore,
        importanceScore: analysis.importanceScore,
        keywords: analysis.keywords,
        analysisData: analysis.analysisData,
        processed: true, // Mark as processed since we've done AI analysis
      };
    } catch (error) {
      console.error(`❌ AI Analysis failed for tweet ${tweet.id}:`, error);
      
      // Return basic mention if AI analysis fails
      return {
        ...baseMention,
        processingError: `AI analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Helper function to add delays between API calls
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get sync statistics for a user
   */
  async getSyncStats(userId: string) {
    try {
      const accounts = await this.prisma.monitoredAccount.findMany({
        where: { userId },
        include: {
          _count: {
            select: { mentions: true },
          },
        },
      });

      const totalAccounts = accounts.length;
      const activeAccounts = accounts.filter(a => a.isActive).length;
      const totalMentions = accounts.reduce((sum: number, account: any) => sum + account._count.mentions, 0);
      const oldestCheck = accounts.reduce((oldest: Date | null, account: any) => {
        if (!account.lastCheckedAt) return oldest;
        if (!oldest) return account.lastCheckedAt;
        return account.lastCheckedAt < oldest ? account.lastCheckedAt : oldest;
      }, null as Date | null);

      return {
        totalAccounts,
        activeAccounts,
        totalMentions,
        lastSyncAt: oldestCheck,
        accountDetails: accounts.map((account: any) => ({
          id: account.id,
          handle: account.twitterHandle,
          displayName: account.displayName,
          isActive: account.isActive,
          lastCheckedAt: account.lastCheckedAt,
          mentionsCount: account._count.mentions,
        })),
      };
    } catch (error) {
      console.error('Error getting sync stats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const mentionSyncService = new MentionSyncService(
  // We'll inject Prisma client when using this service
  {} as PrismaClient
);