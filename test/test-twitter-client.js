/**
 * Direct test of Twitter client to verify the userName parameter fix
 */

// Test the API call directly
async function testTwitterAPI() {
  console.log('🧪 Testing Twitter API directly...');

  const TWITTER_API_KEY = '96e428bef7fa4f078dab3b6c9678b774';
  const TWITTER_API_BASE_URL = 'https://api.twitterapi.io';

  // Test with the old parameter (should fail)
  console.log('🔍 Testing with old parameter (username)...');
  try {
    const url = new URL(`${TWITTER_API_BASE_URL}/twitter/user/info`);
    url.searchParams.append('username', 'marionawfal');

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    const result = await response.text();
    console.log('📄 Old parameter response:', response.status, result);
  } catch (error) {
    console.log('❌ Old parameter error:', error.message);
  }

  // Test with the new parameter (should work)
  console.log('🔍 Testing with new parameter (userName)...');
  try {
    const url = new URL(`${TWITTER_API_BASE_URL}/twitter/user/info`);
    url.searchParams.append('userName', 'marionawfal');

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    const result = await response.text();
    console.log('📄 New parameter response:', response.status, result);

    if (response.ok) {
      console.log('✅ Fix confirmed! userName parameter works');
    } else {
      console.log('❌ Still having issues with userName parameter');
    }
  } catch (error) {
    console.log('❌ New parameter error:', error.message);
  }
}

testTwitterAPI();
