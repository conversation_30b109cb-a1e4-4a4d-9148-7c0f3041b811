# BuddyChip Message Retrieval Optimizations

## Critical Database Configuration Fix

### Current Issue (.env)
```env
DATABASE_URL="...connection_limit=1"
DIRECT_URL="...connection_limit=1"  # Same as DATABASE_URL
```

### Solution
```env
# Pooled connection for app queries (higher limit)
DATABASE_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=10"

# Direct connection for migrations/admin
DIRECT_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:5432/postgres"
```

## Message Retrieval Optimizations

### 1. Optimize mentions.getAll Query (apps/web/src/routers/mentions.ts:124)

**Current**: Loads all responses per mention (N+1 problem)
```typescript
responses: {
  orderBy: { createdAt: 'desc' },
  take: 3, // Still loads for every mention
},
```

**Optimized**: Use conditional loading
```typescript
include: {
  account: {
    select: {
      id: true,
      twitterHandle: true,
      displayName: true,
      avatarUrl: true,
      isActive: true,
    },
  },
  // Only load responses if specifically requested
  ...(input.includeResponses && {
    responses: {
      orderBy: { createdAt: 'desc' },
      take: 3,
      select: {
        id: true,
        content: true,
        model: true,
        confidence: true,
        tokensUsed: true,
        createdAt: true,
      },
    },
  }),
},
```

### 2. Add Pagination Cursor Optimization
**Current**: Uses `id: { lt: cursor }` which isn't indexed well
**Better**: Use timestamp-based cursor with index

```typescript
// In schema.prisma - ensure this index exists
@@index([userId, createdAt(sort: Desc), id])

// In router - optimize cursor logic
if (input.cursor) {
  const [timestamp, id] = input.cursor.split('_');
  where.OR = [
    { createdAt: { lt: new Date(timestamp) } },
    { 
      createdAt: new Date(timestamp),
      id: { lt: id }
    }
  ];
}
```

### 3. Implement Response Caching

**Add to tRPC client configuration**:
```typescript
export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: '/api/trpc',
      maxURLLength: 2083,
    }),
  ],
});

// Add React Query caching config
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
      refetchOnWindowFocus: false,
    },
  },
});
```

### 4. Database-Level Optimizations

**Add missing indexes**:
```sql
-- Composite index for common queries
CREATE INDEX CONCURRENTLY idx_mentions_user_created_processed 
ON mentions (user_id, created_at DESC, processed);

-- Index for responses by mention
CREATE INDEX CONCURRENTLY idx_ai_responses_mention_created 
ON ai_responses (mention_id, created_at DESC);

-- Partial index for active mentions
CREATE INDEX CONCURRENTLY idx_mentions_active 
ON mentions (user_id, created_at DESC) 
WHERE archived = false;
```

### 5. Batch Loading Implementation

**Create optimized batch loader**:
```typescript
// apps/web/src/lib/batch-loaders.ts
import DataLoader from 'dataloader';

export const createMentionResponseLoader = (prisma) => 
  new DataLoader(async (mentionIds: string[]) => {
    const responses = await prisma.aIResponse.findMany({
      where: { mentionId: { in: mentionIds } },
      orderBy: { createdAt: 'desc' },
      take: 3,
    });
    
    // Group by mentionId
    const grouped = mentionIds.map(id => 
      responses.filter(r => r.mentionId === id)
    );
    
    return grouped;
  });
```

### 6. Frontend Virtualization

**For large lists, implement virtual scrolling**:
```typescript
// Use react-window or @tanstack/react-virtual
import { useVirtualizer } from '@tanstack/react-virtual';

const MentionsList = ({ mentions }) => {
  const virtualizer = useVirtualizer({
    count: mentions.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 200, // Estimated height per item
  });

  return (
    <div style={{ height: virtualizer.getTotalSize() }}>
      {virtualizer.getVirtualItems().map((virtualItem) => (
        <MentionCard 
          key={virtualItem.key}
          mention={mentions[virtualItem.index]}
          style={{
            transform: `translateY(${virtualItem.start}px)`,
          }}
        />
      ))}
    </div>
  );
};
```

## Performance Monitoring

### Add Query Performance Logging
```typescript
// apps/web/src/lib/prisma.ts
const prisma = new PrismaClient({
  log: [
    { emit: 'event', level: 'query' },
    { emit: 'event', level: 'slow' },
  ],
});

prisma.$on('query', (e) => {
  if (e.duration > 1000) { // Log slow queries
    console.warn(`Slow query (${e.duration}ms):`, e.query);
  }
});
```

## Implementation Priority

1. **🔴 Critical**: Fix database connection limits in .env
2. **🟠 High**: Add conditional response loading
3. **🟡 Medium**: Implement cursor-based pagination optimization  
4. **🟢 Low**: Add virtual scrolling for large lists

## Expected Performance Gains

- **Database connections**: 90% reduction in connection timeouts
- **Query performance**: 60-80% faster mention loading
- **UI responsiveness**: 50% improvement with virtualization
- **Memory usage**: 40% reduction with optimized queries