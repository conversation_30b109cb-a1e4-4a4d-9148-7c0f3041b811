'use client'

import { useState, useRef, useEffect } from 'react'
import { useUser, useClerk } from '@clerk/nextjs'
import { User, Settings, LogOut, ChevronDown } from 'lucide-react'

export default function ProfileDropdown() {
  const { user, isLoaded } = useUser()
  const { signOut } = useClerk()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  if (!isLoaded || !user) {
    return (
      <a href="/sign-in" className="text-app-headline hover:text-app-main transition-colors flex items-center">
        <User className="w-4 h-4 mr-1 md:mr-2" /> LOGIN
      </a>
    )
  }

  const handleSignOut = () => {
    signOut()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Profile Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
        className="text-app-headline hover:text-app-main transition-colors flex items-center space-x-2 group"
      >
        <div className="flex items-center space-x-2">
          <img 
            src={user.imageUrl} 
            alt="Profile" 
            className="w-6 h-6 md:w-8 md:h-8 rounded-full border border-app-stroke"
          />
          <span className="hidden md:block">
            {user.firstName || user.emailAddresses[0].emailAddress}
          </span>
          <ChevronDown className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div 
          className="absolute right-0 mt-2 w-64 bg-app-card border border-app-stroke rounded-lg shadow-lg z-50"
          onMouseLeave={() => setIsOpen(false)}
        >
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-app-stroke">
            <div className="flex items-center space-x-3">
              <img 
                src={user.imageUrl} 
                alt="Profile" 
                className="w-10 h-10 rounded-full border border-app-stroke"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-app-headline truncate">
                  {user.firstName && user.lastName 
                    ? `${user.firstName} ${user.lastName}` 
                    : user.firstName || 'User'
                  }
                </p>
                <p className="text-xs text-app-headline opacity-70 truncate">
                  {user.emailAddresses[0]?.emailAddress}
                </p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <a
              href="/profile"
              className="flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <User className="w-4 h-4 mr-3" />
              View Profile
            </a>
            
            <a
              href="/dashboard"
              className="flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="w-4 h-4 mr-3" />
              Dashboard
            </a>

            <div className="border-t border-app-stroke my-1"></div>
            
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-4 py-2 text-sm text-app-highlight hover:bg-app-background transition-colors"
            >
              <LogOut className="w-4 h-4 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}