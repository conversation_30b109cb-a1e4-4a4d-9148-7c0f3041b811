/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    // Test environment for unit tests
    environment: 'node',
    
    // Setup files for unit tests
    setupFiles: ['./src/test/setup/unit-setup.ts'],
    
    // Unit test patterns - focus on individual functions/classes
    include: [
      'src/lib/**/*.{test,spec}.{ts,tsx}',
      'src/utils/**/*.{test,spec}.{ts,tsx}',
      'src/components/**/*.{test,spec}.{tsx}',
      'src/test/unit/**/*.{test,spec}.{ts,tsx}'
    ],
    exclude: [
      'node_modules',
      'dist',
      '.next',
      'coverage',
      'src/test/integration/**',
      'src/test/e2e/**',
      'src/test/fixtures/**',
      'src/test/mocks/**'
    ],
    
    // Faster timeouts for unit tests
    testTimeout: 10000, // 10 seconds
    hookTimeout: 5000,  // 5 seconds
    
    // Coverage for unit tests
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json'],
      reportsDirectory: './coverage/unit',
      include: [
        'src/lib/**/*.{ts,tsx}',
        'src/utils/**/*.{ts,tsx}',
        'src/components/**/*.{tsx}'
      ],
      exclude: [
        'src/lib/db-utils.ts', // Tested in integration
        'src/lib/context.ts',  // Tested in integration
        '**/*.d.ts',
        '**/*.config.*',
        'src/test/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Parallel execution for unit tests
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 6, // More threads for unit tests
        minThreads: 2
      }
    },
    
    // Test isolation
    isolate: true,
    
    // No retries for unit tests (they should be deterministic)
    retry: 0,
    
    // Reporter configuration
    reporter: ['verbose'],
    
    // Watch mode enabled for development
    watch: false,
    
    // Mock environment variables
    env: {
      NODE_ENV: 'test',
      // Minimal env vars for unit tests
      OPENROUTER_API_KEY: 'mock_key',
      TWITTER_API_KEY: 'mock_key',
      OPENAI_API_KEY: 'mock_key'
    },
    
    // Globals for unit tests
    globals: true
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './'),
      '@/test': resolve(__dirname, './src/test')
    }
  },
  
  // Define configuration
  define: {
    'process.env.NODE_ENV': '"test"'
  }
})
