import Image from "next/image"
import Link from "next/link"

interface LogoProps {
  size?: number
  className?: string
  href?: string
  showText?: boolean
}

export default function Logo({
  size = 160, // Base size for desktop
  className = "",
  href = "/dashboard",
  showText = false // Not needed since Just-Logo.svg is standalone
}: LogoProps) {
  // Suppress unused parameter warning for showText (kept for API compatibility)
  void showText;
  console.log('🔍 Logo: Rendering with size:', size, 'className:', className);

  const logoElement = (
    <div className={`flex items-center ${className}`}>
      <Image
        src="/Just-Logo.svg"
        alt="BuddyChip Logo"
        width={size}
        height={size}
        className="flex-shrink-0 hover:scale-105 transition-transform duration-200 w-auto h-auto max-w-full"
        style={{
          maxWidth: `${size}px`,
          maxHeight: `${size}px`,
        }}
        priority
      />
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="hover:opacity-90 transition-opacity">
        {logoElement}
      </Link>
    )
  }

  return logoElement
}