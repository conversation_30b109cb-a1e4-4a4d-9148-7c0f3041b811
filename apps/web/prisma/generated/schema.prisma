// Prisma 6.9.0 Schema for BuddyChip
// Modern PostgreSQL schema with advanced indexing and optimizations

generator client {
  provider      = "prisma-client-js"
  output        = "../generated"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Core User model linked to Clerk authentication
model User {
  id      String  @id // Clerk user ID (no @default needed)
  email   String? @unique
  name    String?
  avatar  String? // Profile picture URL
  isAdmin <PERSON>an @default(false)

  // Subscription relationship
  plan   SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Restrict)
  planId String

  // Personality settings
  selectedPersonality PersonalityProfile? @relation(fields: [personalityId], references: [id], onDelete: SetNull)
  personalityId       String?
  customSystemPrompt  String? // User's custom system prompt

  // AI Model settings
  selectedModel AIModel? @relation(fields: [modelId], references: [id], onDelete: SetNull)
  modelId       String? // Selected AI model

  // One-to-many relationships
  monitoredAccounts MonitoredAccount[]
  aiResponses       AIResponse[]
  mentions          Mention[] // For tracking user-created mentions
  images            Image[] // User-uploaded images
  usageLogs         UsageLog[] // Rate limiting tracking

  // Timestamps
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  lastActiveAt DateTime? // Track user activity

  // Indexes for performance
  @@index([planId])
  @@index([email])
  @@index([createdAt])
  @@index([lastActiveAt])
  @@index([personalityId])
  @@index([modelId])
  @@map("users")
}

// Personality profiles for AI responses
model PersonalityProfile {
  id           String  @id @default(cuid())
  name         String  @unique // e.g. "Tech Bro", "Crypto Degen"
  description  String // Brief description of the personality
  systemPrompt String // The system prompt template for this personality
  isDefault    Boolean @default(false) // Whether this is the default personality
  isActive     Boolean @default(true) // Whether this personality is available

  // Relationships
  users User[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes
  @@index([name])
  @@index([isActive])
  @@index([isDefault])
  @@map("personality_profiles")
}

// AI Model configurations for different capabilities
model AIModel {
  id          String  @id @default(cuid())
  name        String  @unique // "Workhorse", "Smarty", "Big Brain"
  displayName String // User-friendly display name
  description String // Description of model capabilities
  provider    String // "openrouter" or "openai"
  modelId     String // API model identifier (e.g. "google/gemini-2.5-flash")
  costTier    String // "low", "medium", "high"
  speed       String // "fast", "medium", "slow"
  isActive    Boolean @default(true)

  // Relationships
  users User[] // Users who have selected this model

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes
  @@index([name])
  @@index([isActive])
  @@index([provider])
  @@index([costTier])
  @@map("ai_models")
}

// Subscription plan model with flexible feature configuration
model SubscriptionPlan {
  id                  String   @id @default(cuid())
  name                String   @unique // e.g. "Reply Guy", "Reply God", "Team"
  displayName         String // User-friendly name
  description         String? // Plan description
  price               Decimal  @db.Decimal(10, 2) // Monthly price in USD
  baseUsers           Int      @default(1)
  additionalUserPrice Decimal? @db.Decimal(10, 2) // Price per extra user
  isActive            Boolean  @default(true)

  // Relationships
  features PlanFeature[]
  users    User[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes
  @@index([name])
  @@index([isActive])
  @@map("subscription_plans")
}

// Feature limits per subscription plan
model PlanFeature {
  id      String           @id @default(cuid())
  plan    SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  planId  String
  feature FeatureType // Enum for type safety
  limit   Int // Numeric limit per billing cycle (-1 for unlimited)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Compound unique constraint
  @@unique([planId, feature])
  @@index([planId])
  @@map("plan_features")
}

// Enum for feature types to ensure consistency
enum FeatureType {
  AI_CALLS
  IMAGE_GENERATIONS
  MONITORED_ACCOUNTS
  MENTIONS_PER_MONTH
  MENTIONS_PER_SYNC // How many mentions to fetch per sync operation
  MAX_TOTAL_MENTIONS // Maximum total unreplied mentions per account
  STORAGE_GB
  TEAM_MEMBERS

  @@map("feature_type")
}

// Twitter accounts being monitored
model MonitoredAccount {
  id            String  @id @default(cuid())
  twitterHandle String // e.g. "elonmusk" (without @)
  twitterId     String? // Twitter numeric ID for API calls
  displayName   String? // Twitter display name
  avatarUrl     String? // Twitter profile picture
  isActive      Boolean @default(true)

  // Ownership
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  // Relationships
  mentions Mention[]

  // Monitoring metadata
  lastCheckedAt DateTime? // Last time we checked for mentions
  totalMentions Int       @default(0) // Cache mention count

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@unique([userId, twitterHandle]) // One user can't monitor same account twice
  @@index([userId])
  @@index([twitterId])
  @@index([isActive])
  @@index([lastCheckedAt])
  @@index([twitterHandle])
  @@map("monitored_accounts")
}

// Tweet mentions with enhanced metadata
model Mention {
  id      String @id // Tweet ID from Twitter API
  content String // Tweet text content
  link    String @unique // Full URL to tweet

  // Author information
  authorName      String // Display name
  authorHandle    String // @username
  authorId        String? // Twitter user ID
  authorAvatarUrl String? // Profile picture
  authorVerified  Boolean? // Verification status

  // Tweet metadata
  mentionedAt   DateTime // Original tweet timestamp
  replyCount    Int?     @default(0)
  retweetCount  Int?     @default(0)
  likeCount     Int?     @default(0)
  isReply       Boolean  @default(false)
  parentTweetId String? // If this is a reply

  // AI Analysis
  bullishScore    Int? // 1-100 sentiment score
  importanceScore Int? // 1-100 importance/priority score
  analysisData    Json? // Additional AI analysis results
  keywords        String[] // Extracted keywords for search

  // Relationships
  account   MonitoredAccount? @relation(fields: [accountId], references: [id], onDelete: SetNull)
  accountId String? // Null for quick replies
  responses AIResponse[]
  images    Image[] // Attached images
  user      User?             @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId    String? // User who created this mention

  // Processing status
  processed       Boolean @default(false)
  processingError String? // Store any processing errors

  // Archive status
  archived   Boolean   @default(false) // Whether mention is archived
  archivedAt DateTime? // When mention was archived

  // Tweet type flag
  isUserTweet Boolean @default(false) // True if this is a user's own tweet, not a mention

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Advanced indexes for performance
  @@index([accountId])
  @@index([userId])
  @@index([mentionedAt(sort: Desc)]) // Most recent first
  @@index([bullishScore(sort: Desc)]) // Highest scores first
  @@index([importanceScore(sort: Desc)]) // Highest importance first
  @@index([processed])
  @@index([archived]) // Archive status queries
  @@index([authorHandle])
  @@index([createdAt(sort: Desc)])
  @@index([content])
  @@index([keywords])
  // Compound indexes for common query patterns
  @@index([accountId, mentionedAt(sort: Desc)])
  @@index([userId, createdAt(sort: Desc)])
  @@index([accountId, archived]) // For filtering archived mentions per account
  @@index([userId, archived]) // For filtering archived mentions per user
  @@map("mentions")
}

// AI-generated responses with enhanced tracking
model AIResponse {
  id String @id @default(cuid())

  // Content
  content    String // AI-generated reply text
  model      String? // Which AI model was used
  prompt     String? // Original prompt used
  tokensUsed Int? // Token consumption tracking

  // Relationships
  mention   Mention @relation(fields: [mentionId], references: [id], onDelete: Cascade)
  mentionId String
  user      User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId    String?
  images    Image[] // Generated images

  // Quality metrics
  confidence Float? // AI confidence score (0.0-1.0)
  rating     Int? // User rating (1-5 stars)
  used       Boolean @default(false) // Whether user actually used this response

  // Processing metadata
  processingTime BigInt? // Milliseconds to generate
  version        String? // AI model version

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes
  @@index([mentionId])
  @@index([userId])
  @@index([model])
  @@index([createdAt(sort: Desc)])
  @@index([rating(sort: Desc)])
  @@index([used])
  // Compound indexes
  @@index([userId, createdAt(sort: Desc)])
  @@index([mentionId, createdAt(sort: Desc)])
  @@map("ai_responses")
}

// Image storage with metadata
model Image {
  id String @id @default(cuid())

  // File information
  url       String  @unique // UploadThing file URL
  filename  String? // Original filename
  fileSize  Int? // Size in bytes
  mimeType  String? // MIME type
  uploadKey String? // UploadThing file key for management

  // Image metadata
  width   Int? // Image dimensions
  height  Int?
  altText String? // Accessibility description

  // Relationships
  aiResponse   AIResponse? @relation(fields: [aiResponseId], references: [id], onDelete: SetNull)
  aiResponseId String?
  mention      Mention?    @relation(fields: [mentionId], references: [id], onDelete: SetNull)
  mentionId    String?
  user         User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId       String?

  // Status
  processed Boolean @default(false)
  isPublic  Boolean @default(false)

  // Timestamps
  uploadedAt DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Indexes
  @@index([aiResponseId])
  @@index([mentionId])
  @@index([userId])
  @@index([uploadedAt(sort: Desc)])
  @@index([mimeType])
  @@index([processed])
  @@map("images")
}

// Usage tracking for rate limiting
model UsageLog {
  id String @id @default(cuid())

  // Usage information
  user    User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  String
  feature FeatureType // What feature was used
  amount  Int         @default(1) // How many units consumed

  // Metadata
  metadata Json? // Additional usage context

  // Billing period tracking
  billingPeriod String // e.g. "2024-01" for monthly tracking

  // Timestamps
  createdAt DateTime @default(now())

  // Indexes for rate limiting queries
  @@index([userId, feature])
  @@index([userId, billingPeriod])
  @@index([createdAt(sort: Desc)])
  // Compound index for efficient rate limit checks
  @@index([userId, feature, billingPeriod])
  @@map("usage_logs")
}
