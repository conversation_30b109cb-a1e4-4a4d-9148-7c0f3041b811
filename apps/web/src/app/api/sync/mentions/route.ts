/**
 * API Route for Syncing Mentions
 * 
 * This endpoint can be called by external cron services (like Vercel Cron)
 * to automatically sync mentions for all users' monitored accounts.
 * 
 * POST /api/sync/mentions
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db-utils';
import { MentionSyncService } from '@/lib/mention-sync-service';

// Optional API key for security (can be set in environment variables)
const SYNC_API_KEY = process.env.SYNC_API_KEY;

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API sync endpoint called');

    // Optional API key validation for security
    if (SYNC_API_KEY) {
      const authHeader = request.headers.get('authorization');
      const providedKey = authHeader?.replace('Bearer ', '');
      
      if (!providedKey || providedKey !== SYNC_API_KEY) {
        console.log('❌ Unauthorized sync request');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }
    }

    // Parse optional request body for specific user or account sync
    let targetUserId: string | undefined;
    let targetAccountId: string | undefined;
    let maxUsers = 10; // Default limit to prevent overwhelming the system

    try {
      const body = await request.json();
      targetUserId = body.userId;
      targetAccountId = body.accountId;
      maxUsers = body.maxUsers || maxUsers;
    } catch {
      // JSON parsing failed, use defaults
    }

    const syncService = new MentionSyncService(prisma);
    const results = [];
    let totalNewMentions = 0;
    let totalErrors = 0;

    if (targetUserId) {
      // Sync specific user
      console.log(`🎯 Syncing mentions for specific user: ${targetUserId}`);
      
      if (targetAccountId) {
        // Sync specific account for specific user
        const result = await syncService.syncAccountMentions(targetAccountId, targetUserId);
        results.push(result);
        if (result.success) {
          totalNewMentions += result.newMentions;
        } else {
          totalErrors++;
        }
      } else {
        // Sync all accounts for specific user
        const bulkResult = await syncService.syncAllUserAccounts(targetUserId);
        results.push({
          userId: targetUserId,
          success: bulkResult.success,
          newMentions: bulkResult.totalNewMentions,
          errors: bulkResult.errors,
        });
        totalNewMentions += bulkResult.totalNewMentions;
        totalErrors += bulkResult.errors.length;
      }
    } else {
      // Sync for all users (limited by maxUsers)
      console.log(`🌍 Syncing mentions for all users (max: ${maxUsers})`);
      
      // Get users with active monitored accounts
      const usersWithAccounts = await prisma.user.findMany({
        where: {
          monitoredAccounts: {
            some: {
              isActive: true,
            },
          },
        },
        take: maxUsers,
        orderBy: {
          lastActiveAt: 'desc', // Prioritize recently active users
        },
        include: {
          monitoredAccounts: {
            where: { isActive: true },
            orderBy: { lastCheckedAt: 'asc' }, // Prioritize accounts that haven't been synced recently
          },
        },
      });

      console.log(`📋 Found ${usersWithAccounts.length} users with active monitored accounts`);

      for (const user of usersWithAccounts) {
        try {
          console.log(`⏳ Syncing user ${user.id} (${user.monitoredAccounts.length} accounts)`);
          
          const bulkResult = await syncService.syncAllUserAccounts(user.id);
          
          results.push({
            userId: user.id,
            userEmail: user.email,
            accountCount: user.monitoredAccounts.length,
            success: bulkResult.success,
            newMentions: bulkResult.totalNewMentions,
            errors: bulkResult.errors,
          });

          totalNewMentions += bulkResult.totalNewMentions;
          totalErrors += bulkResult.errors.length;

          // Add delay between users to respect rate limits
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
        } catch (error) {
          console.error(`❌ Failed to sync user ${user.id}:`, error);
          totalErrors++;
          results.push({
            userId: user.id,
            userEmail: user.email,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }

    const response = {
      success: totalErrors === 0,
      timestamp: new Date().toISOString(),
      summary: {
        totalNewMentions,
        totalErrors,
        usersProcessed: results.length,
      },
      results,
    };

    console.log(`🏁 Sync completed: ${totalNewMentions} new mentions, ${totalErrors} errors`);

    return NextResponse.json(response, { 
      status: totalErrors === 0 ? 200 : 207 // 207 = Multi-Status (partial success)
    });

  } catch (error) {
    console.error('❌ Sync API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check sync status
export async function GET() {
  try {
    const syncService = new MentionSyncService(prisma);
    
    // Get aggregate stats across all users
    const totalAccounts = await prisma.monitoredAccount.count({
      where: { isActive: true }
    });
    
    const totalMentions = await prisma.mention.count();
    
    const oldestCheck = await prisma.monitoredAccount.findFirst({
      where: { 
        isActive: true,
        lastCheckedAt: { not: null }
      },
      orderBy: { lastCheckedAt: 'asc' },
      select: { lastCheckedAt: true }
    });

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      stats: {
        totalActiveAccounts: totalAccounts,
        totalMentions: totalMentions,
        oldestLastCheck: oldestCheck?.lastCheckedAt,
      },
    });
  } catch (error) {
    console.error('❌ Sync status error:', error);
    
    return NextResponse.json(
      { 
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}