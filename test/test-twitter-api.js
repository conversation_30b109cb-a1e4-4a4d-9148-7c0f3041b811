/**
 * Test script to verify Twitter API fix
 */

const fetch = require('node-fetch');

async function testTwitterAPI() {
  try {
    console.log('🧪 Testing Twitter API with fixed parameter...');
    
    // Test the accounts.add endpoint with marionawfal
    const response = await fetch('http://localhost:3000/trpc/accounts.add?batch=1', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to add proper auth headers here
      },
      body: JSON.stringify({
        "0": {
          "json": {
            "handle": "marionawfal"
          }
        }
      })
    });

    const result = await response.text();
    console.log('📄 Response status:', response.status);
    console.log('📄 Response body:', result);

  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testTwitterAPI();
